

Based on the HAR file analysis, here's a minimal Chrome extension implementation:

```javascript
// manifest.json (core extension configuration)
{
  "manifest_version": 3,
  "name": "DNABot LLM Extension",
  "version": "0.1",
  "permissions": ["storage", "scripting"],
  "host_permissions": ["*://dnabot-multimodal.telekom.de/*"],
  "action": {
    "default_popup": "popup.html"
  },
  "options_page": "options.html"
}
```

```html
<!-- popup.html (main interface) -->
<!DOCTYPE html>
<html>
<body>
  <textarea id="prompt" rows="4" cols="30"></textarea>
  <button id="submit">Ask</button>
  <div id="response" style="margin-top:10px; white-space: pre-wrap;"></div>
  <script src="popup.js"></script>
</body>
</html>
```

```javascript
// popup.js (core logic)
document.getElementById('submit').addEventListener('click', async () => {
  const prompt = document.getElementById('prompt').value;
  const responseDiv = document.getElementById('response');
  
  responseDiv.textContent = 'Generating...';
  
  try {
    const result = await fetch('https://dnabot-multimodal.telekom.de/api/v1/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${await getApiKey()}`
      },
      body: JSON.stringify({
        prompt: prompt,
        context: {
          // Additional context from HAR analysis
          session_id: await getSessionId(),
          model: "telekom-llm-v2.1"
        }
      })
    });

    const data = await result.json();
    responseDiv.textContent = data.choices[0].message.content;
  } catch (error) {
    responseDiv.textContent = `Error: ${error.message}`;
  }
});

// Helper functions
async function getApiKey() {
  return new Promise(resolve => {
    chrome.storage.local.get(['apiKey'], result => resolve(result.apiKey));
  });
}

async function getSessionId() {
  // Session management based on HAR session patterns
  return crypto.randomUUID();
}
```

```html
<!-- options.html (API key configuration) -->
<!DOCTYPE html>
<input type="password" id="apiKey" placeholder="Enter API Key">
<button id="save">Save</button>
<script>
  document.getElementById('save').addEventListener('click', () => {
    chrome.storage.local.set({ apiKey: document.getElementById('apiKey').value });
  });
</script>
```

Key implementation details from HAR analysis:
1. Authentication uses Bearer token in Authorization header
2. API endpoint requires JSON payload with `prompt` and `context` 
3. Response structure contains `choices[0].message.content`
4. Session management pattern observed in requests
5. Required security headers (CORS, HSTS)

To use:
1. Load unpacked extension in Chrome
2. Set API key in options page
3. Click extension icon to open prompt interface
4. Enter text and receive LLM responses

Would you like me to explain any specific part in more detail or suggest improvements for particular aspects?